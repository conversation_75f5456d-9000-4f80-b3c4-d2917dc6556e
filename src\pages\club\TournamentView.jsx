import React, { useEffect, useState } from "react";
import { Client } from "../../api/client";
import UseToast from "../../lib/hooks/UseToast";

import {
  Box,
  Button,
  Container,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  TextField,
  CircularProgress,
  Grid,
} from "@mui/material";
import TournamentDetailsView from "../../components/common/TournamentDetailsView";
import { Link, useParams, useNavigate } from "react-router-dom";
import UseGlobalContext from "../../lib/hooks/UseGlobalContext";
import {
  hasTournamentStarted,
  isTournamentConducted,
  isTournamentOngoing,
  isWithinRegistrationPeriod,
} from "../../utils/utils";
import BackButton from "../../components/common/BackButton";
import TournamentCancellationModal from "../../components/payment/TournamentCancellationiModal";
import TournamentRoundsModal from "../../components/common/TournamentRoundModal";
import ArbiterAutocompletePopup from "../../components/club/ArbiterAutocompletePopup";

const TournamentView = () => {
  const { title } = useParams();
  const newTitle = encodeURIComponent(title);
  const [tournament, setTournament] = useState({});
  const [loading, setLoading] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [confirmText, setConfirmText] = useState("");
  const [deleteInProgress, setDeleteInProgress] = useState(false);
  const toast = UseToast();
  const { user } = UseGlobalContext();
  const [tournamentOngoing, setTournamentOngoing] = useState(false);
  const [withinRegistrationPeriod, setWithinRegistrationPeriod] =
    useState(false);
  const [tournamentConducted, setTournamentConducted] = useState(false);
  const [cancellationModalOpen, setCancellationModalOpen] = useState(false);
  const navigate = useNavigate();
  const [roundsModalOpen, setRoundsModalOpen] = useState(false);
  const [open, setOpen] = useState(false);
  const [arbiter, setArbiter] = useState([]);

  const fetchArbiterDetails = async (id) => {
    setLoading(true);
    try {
      const response = await Client.get(`/tournament/arbiter-invite/${id}`);
      if (response.data.success) {
        setArbiter(response.data.data)
      } else {
        setArbiter(null)
      }
    } catch (error) {
      console.error("Error fetching arbiter details:", error);
      toast.error("Failed to fetch arbiter details");
    } finally {
      setLoading(false);
    }
  };
  useEffect(() => {
    const fetchTournamentDetails = async () => {
      setLoading(true);
      try {
        const response = await Client.get(`/tournament/${newTitle}`);
        if (response.data.success) {
          setTournament(response.data.data);
          fetchArbiterDetails(response.data.data.id)

          const startDate = response.data?.data?.startDate;
          const endDate = response.data?.data?.endDate;
          const reportingTime = response.data?.data?.reportingTime;

          const ongoing = isTournamentOngoing(
            startDate,
            endDate,
            reportingTime
          );
          setTournamentOngoing(ongoing);

          const start = response.data?.data?.registrationStartDate;
          const end = response.data?.data?.registrationEndDate;
          const endTime = response.data?.data?.registrationEndTime;

          const isOpen = isWithinRegistrationPeriod(start, end, endTime);
          setWithinRegistrationPeriod(isOpen);

          const conducted = isTournamentConducted(endDate);
          setTournamentConducted(conducted);
        } else {
          toast.error("Failed to fetch tournament details");
        }
      } catch (error) {
        console.error("Error fetching tournament details:", error);
        toast.error("Error fetching tournament details");
      } finally {
        setLoading(false);
      }
    };
    fetchTournamentDetails();
  }, [title]);

  const getFileExtension = (mimeType) => {
    const extensions = {
      "image/png": ".png",
      "image/jpeg": ".jpg",
      "image/jpg": ".jpg",
      "application/pdf": ".pdf",
      "application/msword": ".doc",
      "application/vnd.openxmlformats-officedocument.wordprocessingml.document":
        ".docx",
      "text/plain": ".txt",
      "application/vnd.ms-excel": ".xls",
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet":
        ".xlsx",
      "application/vnd.ms-powerpoint": ".ppt",
      "application/vnd.openxmlformats-officedocument.presentationml.presentation":
        ".pptx",
    };
    return extensions[mimeType] || "";
  };

  const handleBrochureDownload = async ({ id, title }) => {
    try {
      // Request a short-lived pre-signed URL from your backend
      const response = await Client.get(`/tournament/${id}/brochure`);
      const presignedUrl = response.data.data;

      if (!presignedUrl) {
        throw new Error("No download URL received from server");
      }

      // Use the pre-signed URL (typically valid for 5-15 minutes)
      const fileResponse = await fetch(presignedUrl);

      if (!fileResponse.ok) {
        throw new Error(
          `Failed to download file: ${fileResponse.status} ${fileResponse.statusText}`
        );
      }

      const blob = await fileResponse.blob();

      if (blob.size === 0) {
        throw new Error("Downloaded file is empty");
      }

      // Sanitize filename to remove invalid characters
      const sanitizedTitle =
        title.replace(/[<>:"/\\|?*]/g, "").trim() || "download";

      // Get extension from Content-Type header first, then fallback to blob type
      const contentType = fileResponse.headers.get("content-type");
      const extension =
        getFileExtension(contentType) || getFileExtension(blob.type) || "";

      // Download the file
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;
      link.setAttribute("download", sanitizedTitle + "_Brochure" + extension);
      document.body.appendChild(link);
      link.click();

      // Clean up
      link.parentNode.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error("Error downloading document:", error);
      // You might want to show a user-friendly error message here
      throw error;
    }
  };

  // Open delete confirmation dialog
  const handleOpenDeleteDialog = () => {
    setDeleteDialogOpen(true);
    setConfirmText("");
  };

  // Close delete confirmation dialog
  const handleCloseDeleteDialog = () => {
    setDeleteDialogOpen(false);
    setConfirmText("");
  };

  // Handle text change in confirmation field
  const handleConfirmTextChange = (event) => {
    setConfirmText(event.target.value);
  };

  // Delete tournament after confirmation
  const handleDeleteTournament = async () => {
    if (confirmText !== "delete") {
      toast.error("Please type 'delete' to confirm");
      return;
    }

    setDeleteInProgress(true);
    try {
      const response = await Client.delete(`/tournament/${newTitle}`);
      if (response.data.success) {
        toast.success("Tournament deleted successfully");
        setDeleteDialogOpen(false);
        // Redirect to tournaments list after successful deletion
        window.location.href = "/dashboard/tournaments";
      } else {
        toast.error("Failed to delete tournament");
      }
    } catch (error) {
      console.error("Error deleting tournament:", error);
      toast.error("Error deleting tournament");
    } finally {
      setDeleteInProgress(false);
    }
  };
  // Open cancellation modal
  const handleOpenCancellationModal = () => {
    setCancellationModalOpen(true);
  };

  // Handle successful cancellation
  const handleCancellationSuccess = (data) => {
    toast.success("Tournament cancelled successfully with refunds processed");

    // Refresh the page or redirect
    setTimeout(() => {
      navigate("/dashboard/tournaments");
    }, 1500);
  };
  // Handle successful cancellation
  // const handleCancellationSuccess = (data) => {
  //   // If player cancellation, update registration status
  //   if (!isClubOwner) {
  //     setIsRegistered(false);
  //   }

  //   // If club owner cancellation, update tournament status
  //   if (isClubOwner) {
  //     setTournament((prev) => ({
  //       ...prev,
  //       status: "cancelled",
  //     }));
  //   }

  //   // Refresh the page after a short delay
  //   setTimeout(() => {
  //     window.location.reload();
  //   }, 1500);
  // };

  return (
    <Container maxWidth="xl" sx={{ pt: 2, pb: 4, minHeight: "70dvh" }}>
      <BackButton />
      {loading ? (
        <Box sx={{ display: "flex", justifyContent: "center", my: 4 }}>
          <CircularProgress />
        </Box>
      ) : (
        tournament && (
          <>
            {user?.role === "club" &&
              !hasTournamentStarted(tournament?.startDate) && (
                <Box
                  sx={{
                    display: "flex",
                    justifyContent: "flex-end",
                    gap: 2,
                    alignItems: "center",
                    py: 2,
                  }}
                >
                  <Button
                    fullWidth
                    variant="contained"
                    color="success"
                    component="a"
                    href={`/dashboard/tournaments/edit/${newTitle}`}
                    sx={{
                      borderRadius: 1,
                      textTransform: "none",
                      fontWeight: 500,
                      fontSize:{xs:"0.8rem",md:"1rem"},
                      maxWidth: "100px",
                      px: {xs: 1, md: 2},
                      bgcolor: "rgb(21, 135, 55)",
                      "&:hover": {
                        bgcolor: "rgb(16, 110, 45)",
                      },
                    }}
                  >
                    Edit
                  </Button>
                  <Button
                    fullWidth
                    variant="contained"
                    color="error"
                    onClick={handleOpenDeleteDialog}
                    sx={{
                      borderRadius: 1,
                      textTransform: "none",
                      fontWeight: 500,
                      fontSize:{xs:"0.8rem",md:"1rem"},
                      maxWidth: "100px",
                   px: {xs: 1, md: 2},
                      bgcolor: "rgb(206, 16, 13)",
                      "&:hover": {
                        bgcolor: "rgb(165, 20, 10)",
                      },
                    }}
                  >
                    Delete
                  </Button>
                  <Button
                    fullWidth
                    variant="outlined"
                    color="error"
                    onClick={handleOpenCancellationModal}
                    sx={{
                      borderRadius: 1,
                      textTransform: "none",
                      fontWeight: 500,
                      fontSize:{xs:"0.8rem",md:"1rem"},
                      maxWidth: "200px",
                      textWrap: "balance",

                      px: {xs: 1, md: 2},
                    }}
                  >
                    Cancel & Refund
                  </Button>
                </Box>
              )}
            <TournamentDetailsView tournaments={tournament} />
            <Grid
              container
              spacing={5}
              sx={{
                py: 2,
                borderBottom: "1px solid #f0f0f0",
                justifyContent: "center",
                alignItems: "center",
              }}
            >
              {tournament?.chatUrl &&
                tournament?.chatUrl.startsWith("http") && (
                  <Grid
                    item
                    xs={12}
                    sm={6}
                    md={4}
                    sx={{ display: "flex", justifyContent: "center" }}
                  >
                    <Button
                      variant="contained"
                      color="success"
                      type="a"
                      fullWidth
                      target="_blank"
                      rel="noopener noreferrer"
                      href={tournament?.chatUrl || "#"}
                      sx={{
                        borderRadius: 1,
                        textTransform: "none",
                        fontWeight: 500,
                        fontSize: 16,
                        maxWidth: "250px",
                        px: 2,
                        bgcolor: "#166DA3", // Default background

                        "&:hover": {
                          bgcolor: "#1A7FBF", // Lighter blue on hover
                          boxShadow: "0 6px 12px rgba(0, 0, 0, 0.2)",
                          transform: "translateY(-2px)",
                          transition: "all 0.3s ease",
                        },

                        "&:active": {
                          bgcolor: "#125B87", // Slightly darker blue for active click
                          boxShadow: "inset 0 3px 6px rgba(0, 0, 0, 0.2)",
                          transform: "translateY(0)", // Reset push on click
                        },
                      }}
                    >
                      Chat Now
                    </Button>
                  </Grid>
                )}
              {tournament.brochureUrl && (
                <Grid
                  item
                  xs={12}
                  sm={6}
                  md={4}
                  sx={{ display: "flex", justifyContent: "center" }}
                >
                  <Button
                    variant="contained"
                    type="button"
                    fullWidth
                    onClick={() =>
                      handleBrochureDownload({
                        id: tournament.id,
                        title: tournament.title,
                      })
                    }
                    color="success"
                    sx={{
                      borderRadius: 1,
                      textTransform: "none",
                      fontWeight: 500,
                      fontSize: 16,
                      color: "white",
                      px: 2,
                      maxWidth: "250px",
                      bgcolor: "#166DA3",
                      "&:hover": {
                        bgcolor: "#166DA3",
                      },
                    }}
                  >
                    Download Brochure
                  </Button>
                </Grid>
              )}
              {user?.role === "club" && tournament && (
                <Grid
                  item
                  xs={12}
                  sm={6}
                  md={4}
                  sx={{ display: "flex", justifyContent: "center" }}
                >
                  <Button
                    fullWidth
                    color="primary"
                    onClick={() => setOpen(true)}
                    sx={{
                      borderRadius: 1,
                      textTransform: "none",
                      fontWeight: 500,
                      fontSize: 16,
                      color: "white",
                      px: 2,
                      maxWidth: "250px",
                      bgcolor: "#166DA3",
                      "&:hover": {
                        bgcolor: "#166DA3",
                      },
                    }}
                  >
                    Assign Arbiter
                  </Button>
                </Grid>
              )}
              {user?.role === "club" && withinRegistrationPeriod && (
                <Grid
                  item
                  xs={12}
                  sm={6}
                  md={4}
                  sx={{ display: "flex", justifyContent: "center" }}
                >
                  <Button
                    fullWidth
                    component={Link}
                    to="bulk-registration"
                    color="primary"
                    sx={{
                      borderRadius: 1,
                      textTransform: "none",
                      fontWeight: 500,
                      fontSize: 16,
                      color: "white",
                      px: 2,
                      maxWidth: "250px",
                      bgcolor: "#166DA3",
                      "&:hover": {
                        bgcolor: "#166DA3",
                      },
                    }}
                  >
                    Register Now
                  </Button>
                </Grid>
              )}
              {tournament.locationUrl &&
                tournament.locationUrl.startsWith("http") && (
                  <Grid
                    item
                    xs={12}
                    sm={6}
                    md={4}
                    sx={{ display: "flex", justifyContent: "center" }}
                  >
                    <Button
                      fullWidth
                      variant="contained"
                      color="primary"
                      component="a"
                      href={tournament.locationUrl || "#"}
                      target="_blank"
                      rel="noopener noreferrer"
                      disabled={!tournament.locationUrl}
                      sx={{
                        borderRadius: 1,
                        textTransform: "none",
                        fontWeight: 500,
                        maxWidth: "250px",
                        fontSize: 16,
                        px: 2,
                        bgcolor: "#166DA3", // Default background

                        "&:hover": {
                          bgcolor: "#1A7FBF", // Lighter blue on hover
                          boxShadow: "0 6px 12px rgba(0, 0, 0, 0.2)",
                          transform: "translateY(-2px)",
                          transition: "all 0.3s ease",
                        },

                        "&:active": {
                          bgcolor: "#125B87", // Slightly darker blue for active click
                          boxShadow: "inset 0 3px 6px rgba(0, 0, 0, 0.2)",
                          transform: "translateY(0)", // Reset push on click
                        },
                      }}
                    >
                      View Map
                    </Button>
                  </Grid>
                )}

              {new Date(tournament.registrationStartDate) <= new Date() && (
                <>
                  <Grid
                    item
                    xs={12}
                    sm={6}
                    md={4}
                    sx={{ display: "flex", justifyContent: "center" }}
                  >
                    <Button
                      fullWidth
                      component={Link}
                      to="registeredplayers"
                      color="primary"
                      sx={{
                        borderRadius: 1,
                        textTransform: "none",
                        fontWeight: 500,
                        fontSize: 16,
                        color: "white",
                        px: 2,
                        maxWidth: "250px",
                        bgcolor: "#166DA3", // Default background

                        "&:hover": {
                          bgcolor: "#1A7FBF", // Lighter blue on hover
                          boxShadow: "0 6px 12px rgba(0, 0, 0, 0.2)",
                          transform: "translateY(-2px)",
                          transition: "all 0.3s ease",
                        },

                        "&:active": {
                          bgcolor: "#125B87", // Slightly darker blue for active click
                          boxShadow: "inset 0 3px 6px rgba(0, 0, 0, 0.2)",
                          transform: "translateY(0)", // Reset push on click
                        },
                      }}
                    >
                      Registered Players
                    </Button>
                  </Grid>
                  <Grid
                    item
                    xs={12}
                    sm={6}
                    md={4}
                    sx={{ display: "flex", justifyContent: "center" }}
                  >
                    <Button
                      fullWidth
                      component={Link}
                      to="pairing-details"
                      color="primary"
                      sx={{
                        borderRadius: 1,
                        textTransform: "none",
                        fontWeight: 500,
                        fontSize: 16,
                        color: "white",
                        px: 2,
                        maxWidth: "250px",
                        bgcolor: "#166DA3", // Default background

                        "&:hover": {
                          bgcolor: "#1A7FBF", // Lighter blue on hover
                          boxShadow: "0 6px 12px rgba(0, 0, 0, 0.2)",
                          transform: "translateY(-2px)",
                          transition: "all 0.3s ease",
                        },

                        "&:active": {
                          bgcolor: "#125B87", // Slightly darker blue for active click
                          boxShadow: "inset 0 3px 6px rgba(0, 0, 0, 0.2)",
                          transform: "translateY(0)", // Reset push on click
                        },
                      }}
                    >
                      Pairing Details
                    </Button>
                  </Grid>
                </>
              )}
              {tournamentOngoing && (
                <Grid
                  item
                  xs={12}
                  sm={6}
                  md={4}
                  sx={{ display: "flex", justifyContent: "center" }}
                >
                  <Button
                    fullWidth
                    component={Link}
                    to="attendance"
                    color="primary"
                    sx={{
                      borderRadius: 1,
                      textTransform: "none",
                      fontWeight: 500,
                      fontSize: 16,
                      color: "white",
                      px: 2,
                      maxWidth: "250px",
                      bgcolor: "#166DA3", // Default background

                      "&:hover": {
                        bgcolor: "#1A7FBF", // Lighter blue on hover
                        boxShadow: "0 6px 12px rgba(0, 0, 0, 0.2)",
                        transform: "translateY(-2px)",
                        transition: "all 0.3s ease",
                      },

                      "&:active": {
                        bgcolor: "#125B87", // Slightly darker blue for active click
                        boxShadow: "inset 0 3px 6px rgba(0, 0, 0, 0.2)",
                        transform: "translateY(0)", // Reset push on click
                      },
                    }}
                  >
                    Attendance Marking
                  </Button>
                </Grid>
              )}

              {(tournamentOngoing || tournamentConducted) && (
                <Grid
                  item
                  xs={12}
                  sm={6}
                  md={4}
                  sx={{ display: "flex", justifyContent: "center" }}
                >
                  <Button
                    fullWidth
                    component={Link}
                    to="leader-board"
                    color="primary"
                    sx={{
                      borderRadius: 1,
                      textTransform: "none",
                      fontWeight: 500,
                      fontSize: 16,
                      color: "white",
                      px: 2,
                      maxWidth: "250px",
                      bgcolor: "#166DA3", // Default background

                      "&:hover": {
                        bgcolor: "#1A7FBF", // Lighter blue on hover
                        boxShadow: "0 6px 12px rgba(0, 0, 0, 0.2)",
                        transform: "translateY(-2px)",
                        transition: "all 0.3s ease",
                      },

                      "&:active": {
                        bgcolor: "#125B87", // Slightly darker blue for active click
                        boxShadow: "inset 0 3px 6px rgba(0, 0, 0, 0.2)",
                        transform: "translateY(0)", // Reset push on click
                      },
                    }}
                  >
                    Leader Board
                  </Button>
                </Grid>
              )}
              {user?.role === "arbiter" &&
                (tournamentOngoing || withinRegistrationPeriod) && (
                  <Grid
                    item
                    xs={12}
                    sm={6}
                    md={4}
                    sx={{ display: "flex", justifyContent: "center" }}
                  >
                    <Button
                      fullWidth
                      onClick={() => setRoundsModalOpen(true)}
                      color="primary"
                      sx={{
                        borderRadius: 1,
                        textTransform: "none",
                        fontWeight: 500,
                        fontSize: 16,
                        color: "white",
                        px: 2,
                        maxWidth: "250px",
                        bgcolor: "#166DA3", // Default background

                        "&:hover": {
                          bgcolor: "#1A7FBF", // Lighter blue on hover
                          boxShadow: "0 6px 12px rgba(0, 0, 0, 0.2)",
                          transform: "translateY(-2px)",
                          transition: "all 0.3s ease",
                        },

                        "&:active": {
                          bgcolor: "#125B87", // Slightly darker blue for active click
                          boxShadow: "inset 0 3px 6px rgba(0, 0, 0, 0.2)",
                          transform: "translateY(0)", // Reset push on click
                        },
                      }}
                    >
                      Configure Rounds
                    </Button>
                  </Grid>
                )}

              {/* Certificate Templates Button - Only for club owners */}
              {user?.role === "club" &&
                (tournamentConducted || tournamentOngoing) && (
                  <Grid
                    item
                    xs={12}
                    sm={6}
                    md={4}
                    sx={{ display: "flex", justifyContent: "center" }}
                  >
                    <Button
                      fullWidth
                      component={Link}
                      to="certificates"
                      color="primary"
                      sx={{
                        borderRadius: 1,
                        textTransform: "none",
                        fontWeight: 500,
                        fontSize: 16,
                        color: "white",
                        px: 2,
                        maxWidth: "250px",
                        bgcolor: "#166DA3", // Default background

                        "&:hover": {
                          bgcolor: "#1A7FBF", // Lighter blue on hover
                          boxShadow: "0 6px 12px rgba(0, 0, 0, 0.2)",
                          transform: "translateY(-2px)",
                          transition: "all 0.3s ease",
                        },

                        "&:active": {
                          bgcolor: "#125B87", // Slightly darker blue for active click
                          boxShadow: "inset 0 3px 6px rgba(0, 0, 0, 0.2)",
                          transform: "translateY(0)", // Reset push on click
                        },
                      }}
                    >
                      Certificates
                    </Button>
                  </Grid>
                )}

              {/* Add new buttons here for 5th and 6th in similar pattern */}
            </Grid>
          </>
        )
      )}

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={deleteDialogOpen}
        onClose={handleCloseDeleteDialog}
        aria-labelledby="delete-tournament-dialog-title"
      >
        <DialogTitle
          id="delete-tournament-dialog-title"
          sx={{ fontWeight: 500, fontSize: 18 }}
        >
          Confirm Tournament Deletion
        </DialogTitle>
        <DialogContent>
          <DialogContentText sx={{ mb: 2, fontSize: 16, color: "black" }}>
            Are you sure you want to delete this tournament? This action cannot
            be undone. Type <strong style={{ color: "red" }}>delete</strong> to
            confirm.
          </DialogContentText>
          <TextField
            autoFocus
            margin="dense"
            id="confirmation"
            label="Type 'delete' to confirm"
            type="text"
            fullWidth
            variant="outlined"
            value={confirmText}
            onChange={handleConfirmTextChange}
            error={confirmText !== "" && confirmText !== "delete"}
            sx={{ "& .MuiInputLabel-root": { color: "black" } }}
            helperText={
              confirmText !== "" && confirmText !== "delete"
                ? "Please type 'delete' exactly"
                : ""
            }
          />
        </DialogContent>
        <DialogActions>
          <Button
            onClick={handleCloseDeleteDialog}
            color="primary"
            sx={{ fontSize: 16 }}
          >
            Cancel
          </Button>
          <Button
            onClick={handleDeleteTournament}
            color="error"
            disabled={confirmText !== "delete" || deleteInProgress}
            variant="contained"
            sx={{
              bgcolor: "rgb(206, 16, 13)",
              "&:hover": {
                bgcolor: "rgb(165, 20, 10)",
              },
              fontSize: 16,
            }}
            startIcon={
              deleteInProgress ? (
                <CircularProgress size={20} color="inherit" />
              ) : null
            }
          >
            {deleteInProgress ? "Deleting..." : "Delete Tournament"}
          </Button>
        </DialogActions>
      </Dialog>
      {/* Cancellation Modal */}
      {user?.role === "club" && (
        <TournamentCancellationModal
          open={cancellationModalOpen}
          onClose={() => setCancellationModalOpen(false)}
          tournament={tournament}
          onCancellationSuccess={handleCancellationSuccess}
        />
      )}

      {/* Rounds Configuration Modal */}
      {user?.role === "arbiter" && (
        <TournamentRoundsModal
          open={roundsModalOpen}
          onClose={() => setRoundsModalOpen(false)}
          title={newTitle}
        />
      )}
      {user?.role === "club" && tournament && (
        <ArbiterAutocompletePopup
          open={open}
          id={tournament.id}
          onClose={() => setOpen(false)}
          name={'Select Arbiter'}
          tournament={tournament}
          arbiter={arbiter}
          onsuccess={() =>{setOpen(false); fetchArbiterDetails(tournament.id)}}
        />)}
    </Container>
  );
};

export default TournamentView;
