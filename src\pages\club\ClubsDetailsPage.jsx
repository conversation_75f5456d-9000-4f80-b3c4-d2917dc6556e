import React, { useEffect, useState } from "react";
import {
  Avatar,
  Box,
  Button,
  Container,
  Paper,
  Skeleton,
  Stack,
  Typography,
} from "@mui/material";

import AccountBalanceIcon from "@mui/icons-material/AccountBalance";
import { Client } from "../../api/client";
import { useNavigate, useParams } from "react-router-dom";
import UseToast from "../../lib/hooks/UseToast";
import EnquireButton from "../../components/common/EnquireButton";
import { DetailTable } from "../../components/common/DetailTable";
import UseGlobalContext from "../../lib/hooks/UseGlobalContext";
import ClubInvitationModal from "../../components/club/ClubInvitationModal";
import BackButton from "../../components/common/BackButton";
import { formatPhoneNumber } from "../../utils/formatters";
const ClubsDetailsPage = () => {
  const [clubInfo, setClubInfo] = useState(null);
  const [loading, setLoading] = useState(true);
  const [request, setRequest] = useState([]);

  const [invitationModalOpen, setInvitationModalOpen] = useState(false);
  const toast = UseToast();
  const navigate = useNavigate();
  const { user, setOpenModel } = UseGlobalContext();
  const { id: clubId } = useParams();

  const fetchClubInfo = async () => {
    setLoading(true);

    try {
      const response = await Client.get(`/club/single/${clubId}`);
      if (response.status === 204) {
        navigate("edit");
        return;
      }
      if (!response.data.success) {
        toast.error(response.data.message);

        return;
      }
      setClubInfo(response.data.data);
      if (!user || user?.role === "player") {
        fetchClubInvite(response.data.data.clubName);
      }
      setLoading(false);
    } catch (error) {
      console.error("Error fetching club info:", error);

      setLoading(false);
    }
  };

  const fetchClubInvite = async (id) => {
    setLoading(true);

    try {
      const response = await Client.get(`/user/get-club-request?id=${id}`);
      if (!response.data.success) {
        setRequest(null);
        return;
      }
      setRequest(response.data.data);
      setLoading(false);
    } catch (error) {
      console.error("Error fetching club info:", error);

      setLoading(false);
    }
  };
  const CancelClubInvite = async () => {
    setLoading(true);
    try {
      const id = clubInfo.clubName;

      const response = await Client.post(`/user/cancel-club-request?id=${id}`);
      if (!response.data.success) {
        toast.error(response.data.message);
        return;
      }
      fetchClubInfo();
      setLoading(false);
    } catch (error) {
      console.error("Error fetching club info:", error);

      setLoading(false);
    }
  };

  // Fetch club information
  useEffect(() => {
    fetchClubInfo();
  }, [clubId]);

  const handleJoinClub = async () => {
    if (!user) {
      toast.info("Please login to join the club");
      setTimeout(() => {
        setOpenModel((prev) => ({ ...prev, login: true }));
      }, 300);

      localStorage.setItem("navigateTo", false);

      return;
    }
    setInvitationModalOpen(true);
  };

  // Format club data for display
  const formattedClubData = formatClubData(clubInfo);

  return (
    <Container maxWidth="xl" sx={{ py: 4, pt: 2 }}>
      <BackButton />
      <Paper
        elevation={3}
        sx={{
          bgcolor: "background.default",
          minHeight: "80vh",
          borderRadius: 2,
          overflow: "hidden",
        }}
      >
        {/* Header */}
        {clubInfo && (
          <Box
            sx={{
              display: "flex",
              alignItems: { xs: "flex-start", sm: "center" },
              justifyContent: "space-between",
              flexDirection: { xs: "column", sm: "row" },
              p: 2,
              gap: 2,
              px: { xs: "2vw", sm: "10vw" },
              py: 2,
              borderBottom: "1px solid #f0f0f0",
            }}
          >
            <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
              <Avatar
                src={clubInfo.profileUrl}
                sx={{
                  width: {
                    xs: 80,
                    sm: 80,
                    md: 100,
                    lg: 100,
                    xl: 100,
                  },
                  height: {
                    xs: 80,
                    sm: 80,
                    md: 100,
                    lg: 100,
                    xl: 100,
                  },
                  bgcolor: "#f5f5f5",
                  color: "#000",
                  border: "1px solid #********",
                }}
              >
                {!clubInfo.profileUrl && (
                  <AccountBalanceIcon sx={{ fontSize: { xs: 50, md: 60 } }} />
                )}
              </Avatar>
              <Stack>
                {!loading ? (
                  <>
                    <Typography
                      variant="h4"
                      component="h4"
                      fontWeight="500"
                      sx={{ fontSize: { xs: "20px", sm: "20px", md: "24px" } }}
                    >
                      {formattedClubData.title}
                    </Typography>
                  </>
                ) : (
                  <Skeleton variant="text" width={200} />
                )}
              </Stack>
            </Box>
            <Box>
              {(!user || user?.role === "player") && (
                <>
                  {request !== null ? (
                    <Button
                      size={"small"}
                      variant="contained"
                      sx={{
                        borderRadius: "4px",
                        textTransform: "none",
                        fontWeight: "bold",
                        mx: 1,
                        bgcolor: "#2C832C",
                        color: "#fff",
                        px: 4,
                        "&:hover": {
                          bgcolor: "#236723",
                        },
                      }}
                      onClick={() => CancelClubInvite()}
                    >
                      Cancel Request
                    </Button>
                  ) : (
                    <Button
                      size={"small"}
                      variant="contained"
                      sx={{
                        borderRadius: "4px",
                        textTransform: "none",
                        fontWeight: "bold",
                        mx: 1,
                        bgcolor: "#2C832C",
                        color: "#fff",
                        px: 4,
                        "&:hover": {
                          bgcolor: "#236723",
                        },
                      }}
                      onClick={handleJoinClub}
                    >
                      Join Club
                    </Button>
                  )}
                </>
              )}

              <EnquireButton
                email={clubInfo}
                variant="contained"
                size={"small"}
                sx={{
                  borderRadius: 1,
                  bgcolor: "#2C832C",
                  color: "#fff",
                  px: 4,
                  "&:hover": {
                    bgcolor: "#236723",
                  },
                }}
              />
            </Box>
          </Box>
        )}

        {/* Club Information */}
        <Box>
          {loading ? (
            // Loading skeleton
            Array(6)
              .fill(0)
              .map((_, index) => (
                <Box key={index} sx={{ mb: 2 }}>
                  <Skeleton variant="rectangular" height={40} />
                </Box>
              ))
          ) : !clubInfo ? (
            // Error message
            <Typography
              color="error"
              variant="h6"
              align="center"
              sx={{ py: 10 }}
            >
              No club Found
            </Typography>
          ) : (
            // Club details table
            <DetailTable details={formattedClubData.details} />
          )}
        </Box>
      </Paper>
      {/* Club Invitation Modal */}
      <ClubInvitationModal
        open={invitationModalOpen}
        onClose={() => setInvitationModalOpen(false)}
        clubData={clubInfo}
        mode="join-request"
      />
    </Container>
  );
};

export default ClubsDetailsPage;

function formatClubData(club) {
  if (!club) {
    return {
      title: "",
      details: [],
    };
  }

  // Create a list of details to display
  const details = [
    {
      label: "Club Name",
      value: club.clubName || "-",
    },
    {
      label: "Country",
      value: club.country || "-",
    },
    {
      label: "State",
      value: club.state || "-",
    },
    {
      label: "District",
      value: club.district || "-",
    },
    {
      label: "City",
      value: club.city || "-",
    },
    {
      label: "Address",
      value: club.address || "-",
    },
    {
      label: "Contact Person",
      value: club.contactPersonName || "-",
    },
    {
      label: "Contact Number",
      value: club.contactPersonNumber
        ? formatPhoneNumber(club.contactPersonNumber)
        : "-",
    },
    {
      label: "Alternate Contact",
      value: club.alternateContactNumber
        ? formatPhoneNumber(club.alternateContactNumber)
        : "-",
    },
    {
      label: "Contact Email",
      value: club.contactPersonEmail || "-",
    },
  ];

  // Add location link if available
  if (club.locationUrl) {
    details.push({
      label: "Location",
      value: "View Map",
      isLink: true,
      url: club.locationUrl,
      // Add target and rel attributes for security and new tab opening
      linkProps: {
        target: "_blank",
        rel: "noopener noreferrer",
      },
    });
  }
  return {
    title: club.clubName || "",
    details: details,
  };
}
