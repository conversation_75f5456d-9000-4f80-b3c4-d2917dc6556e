import React, { useState, useEffect, useCallback } from "react";
import {
  Typo<PERSON>,
  Autocomplete,
  TextField,
  Fade,
  CircularProgress,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Box,
} from "@mui/material";
import { Client } from "../../api/client";
import UseToast from "../../lib/hooks/UseToast";

const ArbiterAutocompletePopup = ({
  open,
  onClose,
  onsuccess,
  id,
  placeholder = "Search for an arbiter",
  title = "Arbiter",
  sx = {},
  required = false,
  disabled = false,
  fetchUrl = "/arbiter/profile/arbiter",
  defaultValue = null,
  tournament,
  arbiter
}) => {
  const [arbiters, setArbiters] = useState([]);
  const [loading, setLoading] = useState(false);
  const [inputValue, setInputValue] = useState("");
  const [selectedArbiter, setSelectedArbiter] = useState(defaultValue);
  const toast = UseToast()

  const fetchArbiters = useCallback(async (searchTerm) => {
    if (!searchTerm?.trim()) {
      setArbiters([]);
      return;
    }

    setLoading(true);
    try {
      const response = await Client.get(`${fetchUrl}?arbiterId=${encodeURIComponent(searchTerm)}`);
      setArbiters(response.data.data || []);
    } catch (error) {
      console.error("Error fetching arbiters:", error);
      setArbiters([]);
    } finally {
      setLoading(false);
    }
  }, [fetchUrl]);

  useEffect(() => {
    const timer = setTimeout(() => {
      if (inputValue.trim()) {
        fetchArbiters(inputValue);
      } else {
        setArbiters([]);
      }
    }, 500);

    return () => clearTimeout(timer);
  }, [inputValue, fetchArbiters]);
  
const updateArbiter = async () => {
  setLoading(true)
  const accepted = tournament?.arbiterId ? true :false
  try {
    const payload ={
      arbiterId:selectedArbiter.id,
      id:tournament.id,
      chiefArbiterName:selectedArbiter.name,
      mail:selectedArbiter.email,
      status:accepted
    }
    const response = await Client.post(`/tournament/arbiter-invite`,
      payload,
      {
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
    if (response.data.success){
    console.log('Arbiter updated successfully:', response.data);
    toast.success('Arbiter updated successfully')
    setTimeout(()=>{
    onsuccess()
    },600)
    }
  } catch (error) {
    console.error('Failed to update arbiter:', error?.response?.data || error.message);
    throw error;
  }finally{
    setLoading(false)
  }
};

const handleCancelInvite = async () => {
  try {
    const payload ={
      id:tournament.id,
      action:'reject'
    }
    const response = await Client.post(`/tournament/arbiter-invite/cancel`,
      payload,
      {
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
    if (response.data.success){
    console.log('Arbiter Invite cancelled:', response.data.message);
    toast.success('Arbiter Invite cancelled')
    setTimeout(()=>{
    onsuccess()
    },100)
    }
  } catch (error) {
    console.error('Failed to update arbiter:', error?.response?.data || error.message);
    toast.error("error in cancel request")
    throw error;
  }
};
  return (
    <Dialog open={open} onClose={onClose} fullWidth maxWidth="sm">
      <DialogTitle>
        {title} {required && <span style={{ color: "red" }}>*</span>}
      </DialogTitle>

      <DialogContent dividers>

        {arbiter !== null ? (
        <Box>
       <Typography>
        You have already sent an invite to this arbiter: {arbiter?.metadata?.arbiter.arbiterName || "-"}</Typography>
       {tournament?.arbiterId &&  <Typography> arbiter Accepted your request </Typography>}
          </Box>
        ):(
        <>
        <Autocomplete
          options={arbiters}
          getOptionLabel={(option) => option?.name || ""}
          value={selectedArbiter || null}
          onChange={(_, newValue) => setSelectedArbiter(newValue || { name: null, id: null })}
          onInputChange={(_, newInputValue) => setInputValue(newInputValue)}
          isOptionEqualToValue={(option, value) => option?.id === value?.id}
          loading={loading}
          disabled={disabled}
          sx={{ mt: 1, ...sx }}
          renderInput={(params) => (
            <TextField
              {...params}
              placeholder={placeholder}
              variant="outlined"
              margin="normal"
              InputProps={{
                ...params.InputProps,
                endAdornment: (
                  <>
                    {loading && <CircularProgress color="inherit" size={20} />}
                    {params.InputProps.endAdornment}
                  </>
                ),
              }}
            />
          )}
          renderOption={(props, option) => (
            <li {...props} key={option.id}>
              {option.name}
            </li>
          )}
        />
    </>    
)}
      </DialogContent>

      <DialogActions>
        <Button onClick={onClose} color="black">Cancel</Button>
        {arbiter ? (
             <Button
                                fullWidth
                                onClick={()=>handleCancelInvite()}
                                color="primary"
                                sx={{
                                  borderRadius: 1,
                                  textTransform: "none",
                                  fontWeight: 500,
                                  fontSize: 16,
                                  color: "white",
                                  px: 2,
                                  maxWidth: "250px",
                                  bgcolor: "#166DA3", // Default background
          
                                  "&:active": {
                                    bgcolor: "#125B87", // Slightly darker blue for active click
                                    boxShadow: "inset 0 3px 6px rgba(0, 0, 0, 0.2)",
                                    transform: "translateY(0)", // Reset push on click
                                  },
                                }}
                              >
                               cancel Request
                              </Button>
        ):(
        <Button onClick={updateArbiter} color="primary" variant="contained" disabled={!selectedArbiter || loading}>
         {loading? 'updating...': "update"}
        </Button>)}
      </DialogActions>
    </Dialog>
  );
};

export default ArbiterAutocompletePopup;
