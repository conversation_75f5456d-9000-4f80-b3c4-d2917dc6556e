import React, { useEffect, useState } from "react";
import {
  Box,
  Container,
  Paper,
  Stack,
  Button,
  Dialog,
  DialogContent,
} from "@mui/material";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { tournamentSchema } from "../../schema/tournament";
import { Client } from "../../api/client";
import UseToast from "../../lib/hooks/UseToast";

// Import form components
import FormTournamentDetails from "../../components/form/FormTournamentDetails";
import FormContactDetails from "../../components/form/FormContactDetails";
import FormPrizeDetails from "../../components/form/FormPrizeDetails";
import FormVenueDetails from "../../components/form/FormVenueDetails";
import FormOtherDetails from "../../components/form/FormOtherDetails";
import { useNavigate } from "react-router-dom";
import TournamentDetailsView from "../../components/common/TournamentDetailsView";
import BackButton from "../../components/common/BackButton";

const CreateTournamentPage = () => {
  const toast = UseToast();
  const [open, setOpen] = useState(false);
  const [submitData, setSubmitData] = useState();
  const navigate = useNavigate();
  const [submitting, setSubmitting] = useState(false);

  // Initialize React Hook Form
  const {
    control,
    handleSubmit,
    setValue,
    watch,
    reset,
    getValues,
    formState: { isValid },
  } = useForm({
    resolver: zodResolver(tournamentSchema),
    defaultValues: {
      title: "",
      organizerName: "",
      fideRated: "false",
      tournamentLevel: "state",
      startDate: "",
      endDate: "",
      reportingTime: "09:00 AM",
      maleAgeCategory: [],
      femaleAgeCategory: [],
      tournamentCategory: "open",
      registrationStartDate: "",
      registrationEndDate: "",
      registrationEndTime: "09:00 AM",
      tournamentDirectorName: "",
      entryFeeCurrency: "INR",
      entryFee: null,
      chatUrl: null,
      timeControl: "classical",
      timeControlDuration: "",
      timeControlIncrement: "",
      tournamentType: "individual",
      tournamentSystem: "swiss-system",
      nationalApproval: "",
      stateApproval: "",
      districtApproval: "",
      contactPersonName: "",
      email: "",
      contactNumber: "",
      alternateContactNumber: "",
      numberOfTrophiesFemale: null,
      numberOfTrophiesMale: null,
      totalCashPrizeCurrency: "INR",
      totalCashPrizeAmount: null,
      country: "",
      state: "",
      district: "",
      city: "",
      pincode: "",
      venueAddress: "",
      nearestLandmark: "",
      locationUrl: "",
      chessboardProvided: false,
      spotEntry: false,
      timerProvided: false,
      parkingFacility: "no",
      foodFacility: ["nil"],
      subTitle: "",
      presentedBy: "",
    },
    mode: "onSubmit",
    reValidateMode: "onChange",
  });

  useEffect(() => {
    // Check for saved draft in localStorage
    const savedDraft = localStorage.getItem("tournamentDraft");
    if (savedDraft) {
      try {
        const draftData = JSON.parse(savedDraft);
        // Set form values from draft
        Object.keys(draftData).forEach((key) => {
          setValue(key, draftData[key]);
        });
        toast.info("Draft loaded successfully");
      } catch (error) {
        console.error("Error loading draft:", error);
      }
    }
  }, []);

  // helper to scroll the correct container
  const onError = (errors) => {
    const firstField = Object.keys(errors)[0];
    const errorData = errors[firstField];

    const sectionElement = document.querySelector(`[name="${firstField}"]`);

    if (sectionElement) {
      const scrollOffset = 100;
      const y =
        sectionElement.getBoundingClientRect().top +
        window.pageYOffset -
        scrollOffset;
      window.scrollTo({ top: y, behavior: "smooth" });

      setTimeout(() => {
        if ("focus" in sectionElement) {
          sectionElement.focus();
        }
      }, 500);
    }

    toast.info(
      `Error in field: ${firstField} – ${errorData?.message || "Invalid input"}`
    );
  };

  // Form submission handler
  const onSubmit = async (data) => {
    setSubmitData(data);
    localStorage.setItem("tournamentDraft", JSON.stringify(data));
    setOpen(true);
  };

  // Save as draft handler
  const handleSaveDraft = () => {
    const currentData = watch();
    localStorage.setItem("tournamentDraft", JSON.stringify(currentData));
    toast.success("Draft saved successfully");
  };

  const handleCreate = async () => {
    setSubmitting(true);
    try {
      // Create FormData to handle file upload
      const formData = new FormData();

      // Add all fields to FormData with special handling for complex fields
      Object.keys(submitData).forEach((key) => {
        const value = submitData[key];

        // Skip preview fields
        if (["brochureFilePreview"].includes(key)) return;

        // Handle file upload
        if (key === "brochure" && value instanceof File) {
          formData.append("brochure", value);
          return;
        }

        // Special handling for array fields based on schema
        if (
          ["maleAgeCategory", "femaleAgeCategory", "foodFacility"].includes(key)
        ) {
          // Ensure these are always sent as JSON arrays
          const arrayValue = Array.isArray(value)
            ? value
            : [value].filter(Boolean);
          if (!arrayValue || arrayValue.length === 0) return;
          formData.append(key, JSON.stringify(arrayValue));
          return;
        }
        if (key === "fideRated") {
          formData.append(key, value === "true" ? true : false);
          return;
        }

        // General handling for other objects and arrays
        if (typeof value === "object" && value !== null) {
          formData.append(key, JSON.stringify(value));
        } else {
          // Simple values (strings, numbers, booleans)
          formData.append(key, value);
        }
      });

      const response = await Client.post("/tournament", formData, {
        headers: {
          "Content-Type": "multipart/form-data",
        },
      });

      if (response.data.success) {
        toast.success("Tournament created successfully");
        navigate("/dashboard/tournaments");
        // Clear draft from localStorage after successful submission
        localStorage.removeItem("tournamentDraft");
        reset(); // Clear form after successful submission
      } else {
        if (response.data.zodErrors) {
          // Show the first Zod validation error
          const zodErrors = response.data.zodErrors;
          const firstZodError = zodErrors[Object.keys(zodErrors)[0]];
          toast.error(firstZodError || "Please correct the validation errors");
        } else {
          toast.error(response.data.error || "Error creating tournament");
        }
      }
    } catch (error) {
      console.error("Error creating tournament:", error);

      // Handle 409 Conflict error specifically
      if (error.response?.status === 409) {
        toast.error(
          error.response?.data?.error ||
            "Tournament already exists or conflicts with existing data. Please check your tournament details."
        );
        return;
      }

      if (error.response?.status === 422 && error.response?.data?.data) {
        const validationErrors = error.response.data.data;
        Object.keys(validationErrors).forEach((key) => {
          if (key !== "_errors" && validationErrors[key]._errors) {
            toast.error(
              `Validation failed - ${key}: ${validationErrors[key]._errors.join(
                ", "
              )}`
            );
          }
        });
      } else if (error.response?.data?.zodErrors) {
        const zodErrors = error.response.data.zodErrors;
        const firstZodError = zodErrors[Object.keys(zodErrors)[0]];
        toast.error(firstZodError || "Please correct the validation errors");
      } else if (!error.response || error.response.status !== 500) {
        toast.error(
          error.response?.data?.error ||
            "An unexpected error occurred. Please try again later."
        );
      } else {
        toast.error(error.response.data.error || "Error creating tournament");
      }
    } finally {
      setSubmitting(false);
    }
  };
  return (
    <Container
      maxWidth="xl"
      sx={{
        bgcolor: "white",
        minHeight: "100vh",
        px: "4vw",
        pt: 2,
      }}
    >
      <BackButton to={"/dashboard"} />
      <Box sx={{ mb: 8 }}>
        <Paper
          elevation={3}
          sx={{
            borderRadius: "30px",
            p: 4,
            "& .MuiTextField-root": {
              mt: 0.5,
              mb: 0.5,
            },
          }}
        >
          <form onSubmit={handleSubmit(onSubmit, onError)}>
            {/* Tournament Details Section */}
            <FormTournamentDetails
              control={control}
              setValue={setValue}
              watch={watch}
            />

            {/* Contact Details Section */}
            <FormContactDetails
              control={control}
              setValue={setValue}
              watch={watch}
            />

            {/* Prize Details Section */}
            <FormPrizeDetails
              control={control}
              setValue={setValue}
              watch={watch}
            />

            {/* Venue Details Section */}
            <FormVenueDetails
              control={control}
              setValue={setValue}
              watch={watch}
              getValues={getValues}
            />

            {/* Other Details Section */}
            <FormOtherDetails control={control} />

            {/* Submit Buttons */}
            <Stack
              direction="row"
              spacing={2}
              justifyContent="center"
              sx={{ mt: 8, mb: 4 }}
            >
              <Button
                variant="outlined"
                size="small"
                type="button"
                onClick={handleSaveDraft}
                sx={{
                  borderRadius: "10px",
                  border: "1px solid #2c2891",
                  color: "#2c2891",
                  fontSize: "20px",
                }}
              >
                Save Draft
              </Button>
              <Button
                variant="contained"
                size="small"
                type="submit"
                // disabled={submitting}
                sx={{
                  borderRadius: "10px",
                  bgcolor: isValid ? "#2c2891" : "#666",
                  fontSize: "20px",
                  "&:hover": {
                    bgcolor: isValid ? "#1a1b60" : "#555",
                  },
                }}
              >
                {/* {submitting ? "Creating..." : "Submit"} */}
                Submit
              </Button>
            </Stack>
          </form>
        </Paper>
      </Box>

      <Dialog
        className="dialog-content-main"
        sx={{
          maxWidth: "100% !important",
          "& .MuiDialog-paper": { maxWidth: "100% !important" },
        }}
        open={open}
        onClose={() => setOpen(false)}
      >
        <DialogContent className="dialog-content">
          <TournamentDetailsView tournaments={getValues()} />
          <Box
            sx={{
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
            }}
          >
            <Button
              variant="contained"
              sx={{ fontSize: 16 }}
              color="success"
              disabled={submitting}
              onClick={handleCreate}
            >
              {submitting ? "Creating..." : "Create"}
            </Button>
          </Box>
        </DialogContent>
      </Dialog>
    </Container>
  );
};

export default CreateTournamentPage;
