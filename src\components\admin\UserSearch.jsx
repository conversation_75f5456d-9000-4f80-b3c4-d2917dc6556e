import React from 'react';
import { TextField, MenuItem, Box, Typography } from '@mui/material';

const roles = ["club", "player", "arbiter"];

const UserSearch = ({ value, onChange, name = "role" }) => {
 return (
    <Box mb={3} sx={{display:'flex',flexDirection:'column',justifyContent:'flex-start'}}>
        <Box>
      <Typography sx={{width:"fit-content"}} variant="h6" gutterBottom>
        Select User Role
      </Typography>
      </Box>
      <TextField
        select
        value={value}
        name={name}
        onChange={onChange}
        fullWidth
        variant="outlined"
        size="small"
      >
        {roles.map((role) => (
          <MenuItem key={role} value={role.toLowerCase()}>
            {role}
          </MenuItem>
        ))}
      </TextField>
    </Box>
  );
};

export default UserSearch;
