import React from "react";
import {
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Typography,
  Skeleton,
  Divider,
  Button,
  Box,
  Stack,
} from "@mui/material";
import { Link } from "react-router-dom";
import CustomPagination from "./CustomPagination";
import { Client } from "../../api/client";

/**
 * DynamicTable - A reusable table component that can handle different column configurations
 *
 * @param {Object} props
 * @param {Array} props.columns - Array of column definitions with keys: id, label, align, format, width
 * @param {Array} props.data - Array of data objects to display in the table
 * @param {boolean} props.loading - Whether the data is currently loading
 * @param {number} props.page - Current page number
 * @param {number} props.totalPages - Total number of pages
 * @param {Function} props.onPageChange - Function to call when page changes
 * @param {string} props.emptyMessage - Message to display when no data is available
 * @param {string} props.detailsPath - Base path for detail links (e.g., '/players/')
 * @param {string} props.idField - Field to use as ID for detail links (e.g., 'cbid')
 * @param {boolean} props.showDetailsButton - Whether to show the "More Details" button
 * @param {number} props.skeletonRows - Number of skeleton rows to show when loading
 * @param {Object} props.tableContainerProps - Props to pass to TableContainer
 */
const DynamicTable = ({
  columns = [],
  data = [],
  loading = false,
  page = 1,
  totalPages = 1,
  onPageChange = () => {},
  emptyMessage = "No Results Found. Refine Your Search Criteria.",
  detailsPath = "",
  idField = "id",
  showDetailsButton = true,
  showBrouchureButton = false,
  skeletonRows = 4,
  tableContainerProps = { sx: { minHeight: "400px" } },
}) => {
  // Function to render cell content based on column definition
  const renderCellContent = (item, column, index) => {
    const value = item[column.id];

    // If column has a custom format function, use it
    if (column.format) {
      return column.format(value, item, index);
    }

    // Default formatting based on value type
    if (value === null || value === undefined || value === "") {
      return "-";
    }

    return value;
  };
  const getFileExtension = (mimeType) => {
    const extensions = {
      "image/png": ".png",
      "image/jpeg": ".jpg",
      "image/jpg": ".jpg",
      "application/pdf": ".pdf",
      "application/msword": ".doc",
      "application/vnd.openxmlformats-officedocument.wordprocessingml.document":
        ".docx",
      "text/plain": ".txt",
      "application/vnd.ms-excel": ".xls",
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet":
        ".xlsx",
      "application/vnd.ms-powerpoint": ".ppt",
      "application/vnd.openxmlformats-officedocument.presentationml.presentation":
        ".pptx",
    };
    return extensions[mimeType] || "";
  };

  const handleBrochureDownload = async ({ id, title }) => {
    try {
      // Request a short-lived pre-signed URL from your backend
      const response = await Client.get(`/tournament/${id}/brochure`);
      const presignedUrl = response.data.data;

      if (!presignedUrl) {
        throw new Error("No download URL received from server");
      }

      // Use the pre-signed URL (typically valid for 5-15 minutes)
      const fileResponse = await fetch(presignedUrl);

      if (!fileResponse.ok) {
        throw new Error(
          `Failed to download file: ${fileResponse.status} ${fileResponse.statusText}`
        );
      }

      const blob = await fileResponse.blob();

      if (blob.size === 0) {
        throw new Error("Downloaded file is empty");
      }

      // Sanitize filename to remove invalid characters
      const sanitizedTitle =
        title
          .replace(/-/g, " ") // Replace hyphens with spaces
          .replace(/\b\w/g, (c) => c.toUpperCase()) // Capitalize each word
          .replace(/[<>:"/\\|?*]/g, "") // Remove invalid filename characters
          .trim() || "download";

      // Get extension from Content-Type header first, then fallback to blob type
      const contentType = fileResponse.headers.get("content-type");
      const extension =
        getFileExtension(contentType) || getFileExtension(blob.type) || "";

      // Download the file
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;
      link.setAttribute("download", sanitizedTitle + "_Brochure" + extension);
      document.body.appendChild(link);
      link.click();

      // Clean up
      link.parentNode.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error("Error downloading document:", error);
      // You might want to show a user-friendly error message here
      throw error;
    }
  };

  return (
    <Paper>
      <TableContainer {...tableContainerProps} sx={{ overflowX: "auto" }}>
        <Table>
          <TableHead>
            <TableRow
              sx={{
                ".MuiTableCell-root": { textWrap: "nowrap" },
                bgcolor: "#CCBEF033",
              }}
            >
              {columns.map((column) => (
                <TableCell
                  key={column.id}
                  align={column.align || "left"}
                  sx={column.width ? { width: column.width } : {}}
                >
                  {column.label}
                </TableCell>
              ))}
              {showDetailsButton && <TableCell align="right"></TableCell>}
            </TableRow>
          </TableHead>
          <TableBody>
            {/* Loading skeletons */}
            {loading &&
              Array(skeletonRows)
                .fill(0)
                .map((_, index) => (
                  <TableRow key={`skeleton-${index}`}>
                    <TableCell
                      colSpan={columns.length + (showDetailsButton ? 1 : 0)}
                    >
                      <Skeleton variant="rectangular" height={40} />
                    </TableCell>
                  </TableRow>
                ))}

            {/* Empty state */}
            {data.length === 0 && !loading ? (
              <TableRow sx={{ bgcolor: "#DAECF81F" }}>
                <TableCell
                  colSpan={columns.length + (showDetailsButton ? 1 : 0)}
                  align="center"
                >
                  <Typography variant="h6">{emptyMessage}</Typography>
                </TableCell>
              </TableRow>
            ) : (
              /* Data rows */
              data.length > 0 &&
              !loading &&
              data.map((item, index) => (
                <React.Fragment key={item[idField] || index}>
                  <TableRow
                    sx={{
                      bgcolor: "#BEDDF026",
                      "&:hover": { bgcolor: "#f5f5f5" },
                      ".MuiTableCell-root": { textWrap: "nowrap" },
                      textDecoration: "none",
                    }}
                  >
                    {columns.map((column) => (
                      <TableCell
                        key={`${item[idField] || index}-${column.id}`}
                        align={column.align || "left"}
                      >
                        {column.id === "playerName" ||
                        column.id === "clubName" ||
                        column.id === "title" ? (
                          <Link
                            to={`${detailsPath}${item[idField]}`}
                            style={{ textDecoration: "none", color: "inherit" }}
                          >
                            {renderCellContent(item, column, index)}
                          </Link>
                        ) : (
                          renderCellContent(item, column, index)
                        )}
                      </TableCell>
                    ))}

                    {showDetailsButton && (
                      <TableCell align="right">
                        <Box
                          gap={1}
                          sx={{ display: "flex", justifyContent: "flex-end" }}
                        >
                          {showBrouchureButton && item.brochureUrl && (
                            <Button
                              variant="contained"
                              color="success"
                              size="small"
                              onClick={() =>
                                handleBrochureDownload({
                                  id: item.id,
                                  title: item.title,
                                })
                              }
                              sx={{
                                borderRadius: 1,
                                textTransform: "none",

                                fontSize: "0.75rem",

                                bgcolor: "hsla(257, 62%, 32%, 0.85)",
                              }}
                            >
                              Brochure
                            </Button>
                          )}

                          <Button
                            variant="contained"
                            size="small"
                            component={detailsPath ? Link : "button"}
                            to={
                              detailsPath
                                ? `${detailsPath}${encodeURIComponent(
                                    item[idField]
                                  )}`
                                : undefined
                            }
                            sx={{
                              bgcolor: "#3f51b5",
                              textTransform: "none",
                              fontSize: "0.75rem",
                            }}
                          >
                            View Details
                          </Button>
                        </Box>
                      </TableCell>
                    )}
                  </TableRow>

                  {/* Divider between rows */}
                  {index < data.length - 1 && (
                    <TableRow>
                      <TableCell
                        colSpan={columns.length + (showDetailsButton ? 1 : 0)}
                        sx={{ p: 0, borderBottom: "none" }}
                      >
                        <Divider />
                      </TableCell>
                    </TableRow>
                  )}
                </React.Fragment>
              ))
            )}
          </TableBody>
        </Table>
      </TableContainer>

      {/* Pagination */}
      {!loading && totalPages > 1 && (
        <Box sx={{ mt: 2, display: "flex", justifyContent: "center" }}>
          <CustomPagination
            totalPages={totalPages}
            currentPage={page}
            onPageChange={onPageChange}
          />
        </Box>
      )}
    </Paper>
  );
};

export default DynamicTable;
